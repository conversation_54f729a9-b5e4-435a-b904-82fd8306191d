# 影片搜索API使用说明

## ⚠️ 重要说明：数据结构

在使用API之前，请先了解数据库的实际结构：

### 📋 字段含义
- **`number`**: 存储 "番号 + 完整标题"，如 `"SSIS-415【自译征用】失败的新职员和优秀的上司出差时住同一间房"`
- **`title`**: 存储 "演员名字"，如 `"香水じゅん"`
- **`tid`**: 唯一标识ID
- **`extracted_number`**: 新增字段，提取的纯番号，如 `"SSIS-415"`

### 🔍 搜索逻辑
- 搜索番号时，实际是在 `number` 字段中搜索
- 由于 `number` 字段包含完整标题，所以搜索 "上司" 也会匹配到相关影片
- 这就是为什么你会看到"匹配到标题"的原因

## 🎯 核心接口

### 1. 精确番号搜索 (推荐)

```python
from movie_search_api import MovieSearchAPI

api = MovieSearchAPI()

# 精确匹配番号开头
results = api.search_by_pure_number("SSIS-415")  # 只匹配以SSIS-415开头的

# 指定分区的精确搜索
results = api.search_by_pure_number("SSIS", fid=36)  # 在亚洲无码分区精确搜索
```

### 2. 内容关键词搜索

```python
# 搜索标题内容
results = api.search_by_content("上司")  # 搜索包含"上司"的影片
results = api.search_by_content("美女", fid=2)  # 在国产原创分区搜索"美女"
```

### 3. 演员名字搜索

```python
# 搜索演员
results = api.search_by_actor("高桥")  # 搜索演员名字包含"高桥"的
```

### 4. 模糊搜索 (兼容旧版)

```python
# 模糊搜索 (会匹配number字段中的任何内容)
results = api.search_by_number("SSIS", exact_match=False)
```

## 🔧 MongoDB正则表达式查询解释

### 查询语法
```python
query = {"number": {"$regex": "SSIS", "$options": "i"}}
```

### 语法说明
- **`$regex`**: MongoDB正则表达式操作符
- **`"SSIS"`**: 搜索模式 (支持正则表达式)
- **`$options: "i"`**: 忽略大小写

### 匹配效果
- 模糊匹配: `{"$regex": "SSIS"}` → 匹配包含"SSIS"的所有内容
- 精确匹配: `{"$regex": "^SSIS"}` → 只匹配以"SSIS"开头的内容

## 📊 其他接口

### 1. 获取影片详细信息

```python
# 根据TID获取详细信息
details = api.get_movie_by_tid(123456)
```

### 2. 高级搜索

```python
# 高级搜索示例
results = api.advanced_search(
    number="SSIS",           # 番号关键词
    title="美女",            # 标题关键词
    fid=36,                  # 分区ID
    date_from="2024-01-01",  # 开始日期
    date_to="2024-12-31",    # 结束日期
    has_magnet=True,         # 必须有磁力链接
    has_images=True,         # 必须有图片
    limit=50                 # 限制结果数量
)
```

## 📋 分区ID (FID) 对照表

| FID | 分区名称 | 集合名称 |
|-----|---------|---------|
| 2 | 国产原创 | domestic_original |
| 36 | 亚洲无码原创 | asia_codeless_originate |
| 37 | 亚洲有码原创 | asia_mosaic_originate |
| 103 | 高清中文字幕 | hd_chinese_subtitles |
| 151 | 4K视频 | 4k_video |
| 160 | VR视频 | vr_video |
| 38 | 欧美无码 | EU_US_no_mosaic |
| 107 | 三级写真 | three_levels_photo |
| 104 | 素人有码 | vegan_with_mosaic |
| 39 | 动漫原创 | anime_originate |

## 📊 返回数据格式

### 搜索结果格式
```python
[
    {
        "_id": "文档ID",
        "number": "影片番号",
        "title": "影片标题",
        "magnet": "磁力链接",
        "post_time": "发布时间",
        "date": "日期",
        "tid": "页面标识",
        "img": ["图片URL列表"],
        "collection": "所属集合名称",
        "image_count": 图片数量,
        "has_magnet": True/False
    }
]
```

### 详细信息格式
```python
{
    "_id": "文档ID",
    "number": "影片番号",
    "title": "影片标题",
    "magnet": "磁力链接",
    "post_time": "发布时间",
    "date": "日期",
    "tid": "页面标识",
    "img": ["图片URL列表"],
    "image_count": 图片数量,
    "has_magnet": True/False
}
```

## 🚀 快速开始

### 方式1: 运行演示
```bash
python movie_search_api.py
```

### 方式2: 交互式搜索
```bash
python movie_search_api.py interactive
```

### 方式3: 在代码中使用
```python
from movie_search_api import MovieSearchAPI

def search_movies():
    api = MovieSearchAPI()

    try:
        # 搜索SSIS系列
        results = api.search_by_number("SSIS")
        print(f"找到 {len(results)} 条结果")

        for movie in results[:5]:
            print(f"番号: {movie['number']}")
            print(f"标题: {movie['title']}")
            print(f"分区: {movie['collection']}")
            print(f"时间: {movie['post_time']}")
            print("-" * 40)

    finally:
        api.disconnect()

if __name__ == "__main__":
    search_movies()
```

## 💡 使用技巧

### 1. 模糊搜索
- 番号支持模糊匹配，如搜索"SSIS"会匹配所有包含"SSIS"的番号
- 不区分大小写

### 2. 分区搜索
- 指定FID可以在特定分区搜索，提高搜索效率
- 不指定FID会在所有分区搜索

### 3. 日期范围搜索
- 支持按日期范围搜索，格式为"YYYY-MM-DD"
- 可以只指定开始日期或结束日期

### 4. 条件筛选
- 可以筛选必须有磁力链接的影片
- 可以筛选必须有图片的影片

## ⚠️ 注意事项

1. **连接管理**: 使用完毕后记得调用 `api.disconnect()` 关闭连接
2. **结果限制**: 默认每次搜索最多返回100条结果，可通过limit参数调整
3. **网络依赖**: 需要稳定的网络连接访问MongoDB数据库
4. **只读权限**: 数据库为只读，无法修改数据

## 🔧 错误处理

```python
try:
    api = MovieSearchAPI()
    if not api.connect():
        print("数据库连接失败")
        return

    results = api.search_by_number("SSIS")
    # 处理结果...

except Exception as e:
    print(f"搜索过程中发生错误: {e}")
finally:
    api.disconnect()
```

## 📈 性能建议

1. **批量查询**: 一次连接处理多个查询，避免频繁连接断开
2. **合理限制**: 使用limit参数控制返回数据量
3. **精确搜索**: 尽量使用具体的番号而不是过于宽泛的关键词
4. **分区指定**: 如果知道分区，指定FID可以提高搜索速度

---

**🎉 现在你可以轻松搜索15万条影片数据了！**
