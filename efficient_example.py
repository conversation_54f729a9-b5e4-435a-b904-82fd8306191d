#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于实际项目结构的高效数据库访问示例
使用FID映射和精确的字段查询
"""

from read_db import SehuatangMongoDB
from datetime import datetime, timedelta

def main():
    """高效示例 - 基于实际项目结构"""
    
    db = SehuatangMongoDB()
    
    try:
        if db.connect():
            print("✓ 数据库连接成功!")
            
            # 1. 使用FID映射获取数据
            print("\n=== 使用FID映射获取数据 ===")
            
            # 根据FID获取数据
            fid_examples = [
                (2, "国产原创"),
                (36, "亚洲无码原创"), 
                (37, "亚洲有码原创"),
                (103, "高清中文字幕"),
                (151, "4K视频"),
                (160, "VR视频")
            ]
            
            for fid, desc in fid_examples:
                data = db.get_data_by_fid(fid, limit=2)
                print(f"{desc} (FID:{fid}): {len(data)} 条样本")
                for item in data:
                    print(f"  TID:{item.get('tid')} - {item.get('number', 'N/A')[:50]}...")
            
            # 2. 按日期查询数据
            print("\n=== 按日期查询数据 ===")
            
            # 查询今天的数据
            today = datetime.now().strftime("%Y-%m-%d")
            today_data = db.get_data_by_date(today)
            print(f"今天 ({today}) 的数据: {len(today_data)} 条")
            
            # 查询昨天的数据
            yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
            yesterday_data = db.get_data_by_date(yesterday)
            print(f"昨天 ({yesterday}) 的数据: {len(yesterday_data)} 条")
            
            # 查询最近一周的数据
            week_ago = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
            print(f"\n最近一周数据统计:")
            for i in range(7):
                date_str = (datetime.now() - timedelta(days=i)).strftime("%Y-%m-%d")
                day_data = db.get_data_by_date(date_str, limit=1000)
                print(f"  {date_str}: {len(day_data)} 条")
            
            # 3. 精确字段查询
            print("\n=== 精确字段查询 ===")
            
            # 查询包含磁力链接的数据
            query_with_magnet = {"magnet": {"$exists": True, "$ne": ""}}
            magnet_data = db.find_documents("domestic_original", query_with_magnet, limit=5)
            print(f"国产原创中有磁力链接的数据: {len(magnet_data)} 条样本")
            
            # 查询包含图片的数据
            query_with_images = {"img": {"$exists": True, "$ne": []}}
            image_data = db.find_documents("asia_codeless_originate", query_with_images, limit=5)
            print(f"亚洲无码中有图片的数据: {len(image_data)} 条样本")
            
            # 4. 高效搜索示例
            print("\n=== 高效搜索示例 ===")
            
            # 在特定分类中搜索
            search_results = {}
            search_term = "美女"
            
            for fid, desc in fid_examples[:4]:  # 只搜索前4个分类
                collection_name = db.get_collection_name_by_fid(fid)
                results = db.get_data_by_title(search_term, collection_name=collection_name)
                search_results[desc] = len(results)
                print(f"{desc}: {len(results)} 条包含'{search_term}'的结果")
            
            # 5. 数据质量分析
            print("\n=== 数据质量分析 ===")
            
            # 分析国产原创数据质量
            domestic_sample = db.get_domestic_original(limit=100)
            if domestic_sample:
                has_magnet = sum(1 for item in domestic_sample if item.get('magnet'))
                has_images = sum(1 for item in domestic_sample if item.get('img'))
                has_title = sum(1 for item in domestic_sample if item.get('title'))
                
                print(f"国产原创数据质量 (样本100条):")
                print(f"  有磁力链接: {has_magnet}/100 ({has_magnet}%)")
                print(f"  有图片: {has_images}/100 ({has_images}%)")
                print(f"  有标题: {has_title}/100 ({has_title}%)")
            
            # 6. 批量数据处理示例
            print("\n=== 批量数据处理示例 ===")
            
            # 获取4K视频数据并分析
            vr_data = db.get_4k_video(limit=50)
            if vr_data:
                # 转换为DataFrame进行分析
                df = db.to_dataframe(vr_data)
                
                # 分析发布时间分布
                if 'post_time' in df.columns:
                    df['post_date'] = df['post_time'].str[:10]  # 提取日期部分
                    date_counts = df['post_date'].value_counts().head()
                    print(f"4K视频发布日期分布 (前5天):")
                    for date, count in date_counts.items():
                        print(f"  {date}: {count} 条")
                
                # 导出数据
                df.to_csv('4k_video_analysis.csv', index=False, encoding='utf-8-sig')
                print(f"✓ 4K视频数据已导出到 4k_video_analysis.csv")
            
            # 7. TID查询示例
            print("\n=== TID查询示例 ===")
            
            # 获取一些TID进行查询
            if domestic_sample:
                sample_tid = domestic_sample[0].get('tid')
                if sample_tid:
                    tid_result = db.get_data_by_tid(sample_tid)
                    if tid_result:
                        print(f"TID {sample_tid} 查询结果:")
                        item = tid_result[0]
                        print(f"  标题: {item.get('title', 'N/A')}")
                        print(f"  番号: {item.get('number', 'N/A')}")
                        print(f"  时间: {item.get('post_time', 'N/A')}")
            
            # 8. 性能统计
            print("\n=== 性能统计 ===")
            stats = db.get_statistics()
            total_records = sum(v for k, v in stats.items() if k.endswith('_count'))
            print(f"数据库总记录数: {total_records:,}")
            
            # 计算各分类占比
            print("各分类数据占比:")
            for fid, desc in fid_examples:
                collection_name = db.get_collection_name_by_fid(fid)
                count = stats.get(f'{collection_name}_count', 0)
                percentage = (count / total_records * 100) if total_records > 0 else 0
                print(f"  {desc}: {count:,} 条 ({percentage:.1f}%)")
            
        else:
            print("✗ 数据库连接失败")
            
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.disconnect()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    print("色花堂MongoDB数据库 - 高效访问示例")
    print("基于实际项目结构和FID映射")
    print("=" * 60)
    main()
