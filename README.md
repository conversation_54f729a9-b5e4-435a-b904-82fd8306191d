# 色花堂MongoDB数据库访问工具

这是一个用于访问色花堂(sehuatang) MongoDB数据库的Python工具包。

## 数据库信息

- **数据库类型**: MongoDB
- **连接方式**: MongoDB Atlas (云数据库)
- **访问权限**: 只读
- **连接字符串**: `mongodb+srv://readonly:<EMAIL>/Cluster0?retryWrites=true&w=majority`

## 数据库结构

数据库包含 11 个集合(Collections)，总计约 **15万条** 数据：

### 集合分类及FID映射
| 集合名称 | 中文名称 | FID | 数据量 |
|---------|---------|-----|--------|
| domestic_original | 国产原创 | 2 | 34,194条 |
| asia_mosaic_originate | 亚洲有码原创 | 37 | 38,540条 |
| asia_codeless_originate | 亚洲无码原创 | 36 | 23,796条 |
| hd_chinese_subtitles | 高清中文字幕 | 103 | 19,064条 |
| old_backup | 旧数据备份 | - | 18,932条 |
| three_levels_photo | 三级写真 | 107 | 5,615条 |
| vr_video | VR视频 | 160 | 3,423条 |
| EU_US_no_mosaic | 欧美无码 | 38 | 3,087条 |
| vegan_with_mosaic | 素人有码 | 104 | 2,594条 |
| 4k_video | 4K视频 | 151 | 1,630条 |
| anime_originate | 动漫原创 | 39 | 503条 |

### 主要字段
- `_id`: MongoDB文档ID
- `magnet`: 磁力链接
- `number`: 番号/标识
- `title`: 标题
- `post_time`: 发布时间 (格式: YYYY-MM-DD HH:MM:SS)
- `date`: 日期
- `tid`: 网站页面标识 (唯一)
- `fid`: 板块标识 (对应上表FID)
- `img`: 图片URL数组

## 安装依赖

```bash
pip install -r requirements.txt
```

需要安装的包：
- `pymongo`: MongoDB Python驱动
- `pandas`: 数据分析库
- `dnspython`: DNS解析库（MongoDB Atlas需要）

## 使用方法

### 1. 基本使用

```python
from read_db import SehuatangMongoDB

# 创建数据库连接（使用内置的连接字符串）
db = SehuatangMongoDB()

# 连接数据库
if db.connect():
    # 获取所有集合
    collections = db.get_collections()

    # 获取最新数据
    data = db.get_sht_data(limit=10)

    # 搜索番号
    results = db.get_data_by_number("SSIS")

    # 搜索标题
    results = db.get_data_by_title("关键词")

    # 全文搜索
    results = db.search_text("搜索文本")

    # 获取统计信息
    stats = db.get_statistics()

    # 关闭连接
    db.disconnect()
```

### 2. 使用FID获取特定分类数据

```python
# 根据FID获取数据
domestic_data = db.get_data_by_fid(2)  # 国产原创
asia_data = db.get_data_by_fid(36)     # 亚洲无码原创
hd_data = db.get_data_by_fid(103)      # 高清中文字幕

# 或使用便捷方法
domestic_data = db.get_domestic_original(limit=50)
asia_data = db.get_asia_codeless(limit=50)
```

### 3. 按日期和TID查询

```python
# 按日期查询
today_data = db.get_data_by_date("2024-01-01")

# 按TID查询特定记录
record = db.get_data_by_tid(123456)

# 在特定集合中按日期查询
domestic_today = db.get_data_by_date("2024-01-01", "domestic_original")
```

### 4. 自定义连接

```python
# 如果需要连接其他MongoDB数据库
custom_db = SehuatangMongoDB(
    connection_string="your_mongodb_connection_string",
    database_name="your_database_name"
)
```

### 3. 运行示例

```bash
python example_usage.py
```

选择运行模式：
1. 完整示例 - 演示所有功能
2. 仅测试连接 - 快速测试数据库连接
3. 探索数据库结构 - 查看数据库中的集合和文档结构

## 主要功能

### SehuatangMongoDB 类方法

- `connect()`: 连接MongoDB数据库
- `disconnect()`: 断开数据库连接
- `get_collections()`: 获取所有集合名称
- `find_documents(collection, query, limit, skip, sort)`: 通用文档查询
- `get_sht_data(limit, skip)`: 获取主要数据
- `get_data_by_number(number)`: 根据番号搜索
- `get_data_by_title(title)`: 根据标题搜索
- `search_text(text)`: 全文搜索
- `get_images_by_data_id(data_id)`: 获取相关图片
- `get_data_with_images(data_id)`: 获取数据及其图片
- `get_statistics()`: 获取数据库统计信息
- `to_dataframe(data)`: 转换为pandas DataFrame

## 注意事项

1. **网络连接**: 需要稳定的互联网连接访问MongoDB Atlas
2. **只读权限**: 数据库为只读权限，无法修改数据
3. **连接限制**: 公开数据库可能有连接数限制
4. **数据格式**: MongoDB文档格式，字段可能因文档而异

## 常见问题

### 连接失败
- 检查网络连接是否正常
- 确认防火墙没有阻止MongoDB连接
- 检查DNS解析是否正常

### 查询为空
- 检查集合名称是否正确
- 检查查询条件是否匹配
- 数据库中可能没有符合条件的数据

### 性能优化
- 使用适当的limit限制返回数据量
- 对于大量数据，使用skip进行分页
- 合理使用查询条件减少数据传输

## 数据分析示例

```python
# 获取数据并转换为DataFrame
data = db.get_sht_data(limit=1000)
df = db.to_dataframe(data)

# 统计各板块数据量
if 'fid' in df.columns:
    fid_stats = df['fid'].value_counts()
    print("板块统计:", fid_stats)

# 按时间排序
if 'post_time' in df.columns:
    df_sorted = df.sort_values('post_time', ascending=False)

# 筛选特定条件
if 'title' in df.columns:
    filtered_data = df[df['title'].str.contains('关键词', na=False)]

# 导出数据
df.to_csv('sehuatang_data.csv', index=False, encoding='utf-8-sig')
```

## 高级用法

### 自定义查询

```python
# 直接使用MongoDB查询语法
query = {
    "post_time": {"$gte": "2023-01-01"},
    "title": {"$regex": "关键词", "$options": "i"}
}
results = db.find_documents("collection_name", query, limit=50)
```

### 批量数据处理

```python
# 分批获取大量数据
all_data = []
batch_size = 100
skip = 0

while True:
    batch = db.get_sht_data(limit=batch_size, skip=skip)
    if not batch:
        break
    all_data.extend(batch)
    skip += batch_size
    print(f"已获取 {len(all_data)} 条数据")
```

## 文件说明

- `read_db.py`: MongoDB数据库访问类
- `example_usage.py`: 详细使用示例
- `requirements.txt`: Python依赖包
- `README.md`: 说明文档

## 数据导出格式

支持导出为多种格式：
- JSON: 保持原始MongoDB文档结构
- CSV: 适合Excel等工具分析
- DataFrame: 便于Python数据分析
