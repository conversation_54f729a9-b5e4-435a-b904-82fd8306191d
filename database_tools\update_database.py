#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动更新数据库脚本
1. 使用mongodump从线上数据库导出数据
2. 转换为SQLite数据库
"""

import os
import subprocess
import shutil
import logging
from datetime import datetime
from pathlib import Path
import sys
import json
import sqlite3
import pandas as pd
from typing import Dict, List

try:
    import bson
    import pymongo
except ImportError:
    print("❌ 缺少必要的Python包，正在安装...")
    subprocess.run([sys.executable, "-m", "pip", "install", "pymongo", "pandas"])
    import bson
    import pymongo

# 配置
MONGODUMP_PATH = r"D:\Program Files\mongodb-database-tools-windows-x86_64-100.12.2\bin\mongodump.exe"
MONGODB_URI = "mongodb+srv://readonly:<EMAIL>/sehuatang"
BACKUP_DIR = "./sehuatang_backup"
OUTPUT_DIR = "./converted_data"
SQLITE_FILENAME = "sehuatang.db"
MONGODUMP_TIMEOUT = 300  # 5分钟

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_database.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BSONConverter:
    """BSON文件转换器"""

    def __init__(self, backup_dir: str = "./sehuatang_backup/sehuatang"):
        self.backup_dir = backup_dir
        self.output_dir = OUTPUT_DIR
        self.ensure_output_dir()

    def ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def list_bson_files(self) -> List[str]:
        """列出所有BSON文件"""
        if not os.path.exists(self.backup_dir):
            logger.error(f"备份目录不存在: {self.backup_dir}")
            return []

        bson_files = [f for f in os.listdir(self.backup_dir) if f.endswith('.bson')]
        logger.info(f"发现 {len(bson_files)} 个BSON文件: {bson_files}")
        return bson_files

    def read_bson_file(self, filename: str) -> List[Dict]:
        """读取BSON文件内容"""
        filepath = os.path.join(self.backup_dir, filename)

        if not os.path.exists(filepath):
            logger.error(f"文件不存在: {filepath}")
            return []

        logger.info(f"读取BSON文件: {filename}")
        documents = []

        try:
            with open(filepath, 'rb') as f:
                while True:
                    try:
                        # 读取文档大小（前4字节）
                        size_bytes = f.read(4)
                        if len(size_bytes) < 4:
                            break

                        doc_size = int.from_bytes(size_bytes, 'little')

                        # 回退并读取完整文档
                        f.seek(-4, 1)
                        doc_bytes = f.read(doc_size)

                        if len(doc_bytes) < doc_size:
                            break

                        # 解码BSON文档
                        doc = bson.decode(doc_bytes)

                        # 转换ObjectId为字符串
                        if '_id' in doc:
                            doc['_id'] = str(doc['_id'])

                        documents.append(doc)

                    except Exception as e:
                        # 到达文件末尾或解析错误
                        break

            logger.info(f"成功读取 {len(documents)} 条文档")
            return documents

        except Exception as e:
            logger.error(f"读取BSON文件失败: {e}")
            return []

    def convert_to_sqlite(self, db_file: str = "sehuatang.db"):
        """转换所有BSON文件为单个SQLite数据库"""
        db_path = os.path.join(self.output_dir, db_file)

        # 删除现有数据库
        if os.path.exists(db_path):
            os.remove(db_path)

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        try:
            bson_files = self.list_bson_files()
            total_records = 0

            for bson_file in bson_files:
                collection_name = bson_file.replace('.bson', '')
                logger.info(f"导入集合: {collection_name}")

                documents = self.read_bson_file(bson_file)
                if not documents:
                    continue

                # 转换为DataFrame
                df = pd.DataFrame(documents)

                # 处理数组字段
                for col in df.columns:
                    if df[col].dtype == 'object':
                        df[col] = df[col].apply(lambda x: json.dumps(x, default=str) if isinstance(x, list) else x)

                # 导入SQLite
                df.to_sql(collection_name, conn, if_exists='replace', index=False)

                # 创建索引
                try:
                    cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{collection_name}_tid ON {collection_name}(tid);")
                    cursor.execute(f"CREATE INDEX IF NOT EXISTS idx_{collection_name}_number ON {collection_name}(number);")
                except:
                    pass

                total_records += len(documents)
                logger.info(f"集合 {collection_name} 导入完成: {len(documents)} 条记录")

            conn.commit()
            logger.info(f"SQLite数据库创建完成: {db_path}")
            logger.info(f"总记录数: {total_records:,}")

            # 显示数据库信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            print(f"\n📊 SQLite数据库信息:")
            print(f"文件路径: {db_path}")
            print(f"表数量: {len(tables)}")
            print(f"总记录数: {total_records:,}")

            for (table_name,) in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor.fetchone()[0]
                print(f"  {table_name}: {count:,} 条")

        except Exception as e:
            logger.error(f"转换SQLite时发生错误: {e}")
            conn.rollback()
        finally:
            conn.close()

class DatabaseUpdater:
    """数据库更新器"""

    def __init__(self):
        # 配置
        self.mongodump_path = MONGODUMP_PATH
        self.mongodb_uri = MONGODB_URI
        self.backup_dir = BACKUP_DIR
        self.output_dir = OUTPUT_DIR
        self.sqlite_file = SQLITE_FILENAME

        # 创建必要的目录
        self.ensure_directories()

    def ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.backup_dir, self.output_dir]:
            Path(directory).mkdir(parents=True, exist_ok=True)

    def check_mongodump_exists(self) -> bool:
        """检查mongodump工具是否存在"""
        if not os.path.exists(self.mongodump_path):
            logger.error(f"mongodump工具不存在: {self.mongodump_path}")
            logger.error("请检查MongoDB Database Tools的安装路径")
            return False
        return True

    def backup_existing_data(self):
        """备份现有数据"""
        sqlite_path = os.path.join(self.output_dir, self.sqlite_file)
        if os.path.exists(sqlite_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"sehuatang_backup_{timestamp}.db"
            backup_path = os.path.join(self.output_dir, backup_name)

            try:
                shutil.copy2(sqlite_path, backup_path)
                logger.info(f"✅ 已备份现有数据库: {backup_name}")
            except Exception as e:
                logger.warning(f"⚠️ 备份现有数据库失败: {e}")

    def run_mongodump(self) -> bool:
        """执行mongodump导出数据"""
        logger.info("🔄 开始从线上数据库导出数据...")

        # 清理旧的备份目录
        if os.path.exists(self.backup_dir):
            try:
                shutil.rmtree(self.backup_dir)
                logger.info("🗑️ 已清理旧的备份目录")
            except Exception as e:
                logger.warning(f"⚠️ 清理旧备份目录失败: {e}")

        # 构建mongodump命令
        cmd = [
            self.mongodump_path,
            "--uri", self.mongodb_uri,
            "--out", self.backup_dir
        ]

        try:
            # 执行mongodump命令
            logger.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                timeout=MONGODUMP_TIMEOUT
            )

            if result.returncode == 0:
                logger.info("✅ mongodump执行成功")
                logger.info(f"输出: {result.stdout}")
                return True
            else:
                logger.error("❌ mongodump执行失败")
                logger.error(f"错误输出: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("❌ mongodump执行超时（超过5分钟）")
            return False
        except Exception as e:
            logger.error(f"❌ 执行mongodump时发生错误: {e}")
            return False

    def convert_to_sqlite(self) -> bool:
        """转换BSON文件为SQLite"""
        logger.info("🔄 开始转换BSON文件为SQLite...")

        # 检查备份目录是否存在
        sehuatang_backup_dir = os.path.join(self.backup_dir, "sehuatang")
        if not os.path.exists(sehuatang_backup_dir):
            logger.error(f"❌ 备份目录不存在: {sehuatang_backup_dir}")
            return False

        try:
            # 使用BSONConverter转换
            converter = BSONConverter(backup_dir=sehuatang_backup_dir)
            converter.output_dir = self.output_dir

            # 执行转换
            converter.convert_to_sqlite(self.sqlite_file)

            # 检查转换结果
            sqlite_path = os.path.join(self.output_dir, self.sqlite_file)
            if os.path.exists(sqlite_path):
                file_size = os.path.getsize(sqlite_path)
                logger.info(f"✅ SQLite数据库创建成功: {sqlite_path}")
                logger.info(f"📊 文件大小: {file_size / 1024 / 1024:.1f} MB")
                return True
            else:
                logger.error("❌ SQLite数据库文件未创建")
                return False

        except Exception as e:
            logger.error(f"❌ 转换SQLite时发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def cleanup_backup_files(self):
        """清理临时备份文件"""
        try:
            if os.path.exists(self.backup_dir):
                shutil.rmtree(self.backup_dir)
                logger.info("🗑️ 已清理临时备份文件")
        except Exception as e:
            logger.warning(f"⚠️ 清理临时文件失败: {e}")

    def get_database_info(self):
        """获取数据库信息"""
        sqlite_path = os.path.join(self.output_dir, self.sqlite_file)
        if not os.path.exists(sqlite_path):
            return

        try:
            import sqlite3
            conn = sqlite3.connect(sqlite_path)
            cursor = conn.cursor()

            # 获取表信息
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()

            logger.info(f"\n📊 数据库信息:")
            logger.info(f"文件路径: {sqlite_path}")
            logger.info(f"表数量: {len(tables)}")

            total_records = 0
            for (table_name,) in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
                count = cursor.fetchone()[0]
                total_records += count
                logger.info(f"  {table_name}: {count:,} 条记录")

            logger.info(f"总记录数: {total_records:,}")

            conn.close()

        except Exception as e:
            logger.error(f"获取数据库信息失败: {e}")

    def update(self):
        """执行完整的更新流程"""
        start_time = datetime.now()
        logger.info("🚀 开始数据库更新流程...")
        logger.info(f"开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            # 1. 检查mongodump工具
            if not self.check_mongodump_exists():
                return False

            # 2. 备份现有数据
            self.backup_existing_data()

            # 3. 执行mongodump
            if not self.run_mongodump():
                logger.error("❌ 数据导出失败，更新中止")
                return False

            # 4. 转换为SQLite
            if not self.convert_to_sqlite():
                logger.error("❌ 数据转换失败，更新中止")
                return False

            # 5. 显示数据库信息
            self.get_database_info()

            # 6. 清理临时文件
            self.cleanup_backup_files()

            # 计算耗时
            end_time = datetime.now()
            duration = end_time - start_time

            logger.info(f"✅ 数据库更新完成!")
            logger.info(f"结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"总耗时: {duration.total_seconds():.1f} 秒")

            return True

        except Exception as e:
            logger.error(f"❌ 更新过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False


def main():
    """主函数"""
    print("🔄 色花堂数据库自动更新工具")
    print("=" * 50)

    updater = DatabaseUpdater()
    success = updater.update()

    if success:
        print("\n🎉 数据库更新成功!")
        print(f"SQLite数据库位置: {os.path.join(updater.output_dir, updater.sqlite_file)}")
        print("现在可以使用 read_db_sqlite.py 访问数据了")
    else:
        print("\n❌ 数据库更新失败，请检查日志")


if __name__ == "__main__":
    main()
