# 搜索功能说明

## 🔄 修改后的 `search_by_content` 函数

### 功能描述
现在 `search_by_content` 函数会**同时在 `number` 字段和 `title` 字段中搜索**，并对结果进行去重。

### 搜索逻辑
```python
# MongoDB查询条件
query = {
    "$or": [
        {"number": {"$regex": keyword, "$options": "i"}},  # 在number字段搜索
        {"title": {"$regex": keyword, "$options": "i"}}   # 在title字段搜索
    ]
}
```

### 去重机制
- 优先使用 `tid` 字段去重（唯一标识）
- 如果没有 `tid`，则使用 `_id` 字段去重
- 确保同一条记录不会因为在不同字段匹配而重复出现

## 📊 测试结果对比

### 测试案例：搜索 "高桥"

```
搜索 高桥 (高清中文字幕): 14 条去重后结果
仅搜索number字段: 0 条结果
仅搜索title字段: 14 条结果
```

**分析：**
- "高桥" 只在 `title` 字段（演员名字）中找到匹配
- 在 `number` 字段中没有找到匹配
- 最终结果 = title字段结果，无重复

### 测试案例：搜索 "美女"

```
搜索 美女 (国产原创): 100 条去重后结果 (原始结果 100 条)
```

**分析：**
- "美女" 在 `number` 字段（标题内容）中找到匹配
- 可能在 `title` 字段中也有匹配
- 原始结果和去重后结果相同，说明没有重复记录

## 🎯 使用方法

### API调用
```python
from movie_search_api import MovieSearchAPI

api = MovieSearchAPI()

# 搜索会同时匹配number和title字段
results = api.search_by_content("美女")  # 全库搜索
results = api.search_by_content("高桥", fid=103)  # 指定分区搜索

api.disconnect()
```

### 返回结果格式
```python
[
    {
        "_id": "文档ID",
        "number": "番号+完整标题",
        "title": "演员名字",
        "tid": "唯一标识",
        "post_time": "发布时间",
        "magnet": "磁力链接",
        "img": ["图片URL列表"],
        "collection": "所属集合",
        "extracted_number": "提取的纯番号"
    }
]
```

## 🔍 搜索场景示例

### 1. 搜索演员名字
```python
# 搜索演员"高桥"相关的影片
results = api.search_by_content("高桥")
# 会在title字段中找到演员名字包含"高桥"的记录
```

### 2. 搜索内容关键词
```python
# 搜索标题包含"美女"的影片
results = api.search_by_content("美女")
# 会在number字段中找到标题包含"美女"的记录
```

### 3. 搜索可能在多个字段出现的词
```python
# 搜索"学生"
results = api.search_by_content("学生")
# 可能同时匹配：
# - number字段: "SSIS-123【中文字幕】美丽学生的诱惑"
# - title字段: "学生妹小美"
# 结果会自动去重
```

## ⚡ 性能优化

### 去重算法
```python
# 基于tid字段去重
seen_tids = set()
unique_results = []
for item in results:
    tid = item.get('tid')
    if tid and tid not in seen_tids:
        seen_tids.add(tid)
        unique_results.append(item)
```

### 排序规则
- 按 `post_time` 字段降序排序
- 最新的记录排在前面

## 🔧 技术细节

### MongoDB $or 操作符
- `$or` 操作符执行逻辑OR查询
- 匹配任一条件的文档都会被返回
- 自动处理重复文档（MongoDB层面）

### 应用层去重
- MongoDB可能返回重复文档（在不同字段匹配同一记录）
- 应用层使用 `tid` 进行二次去重
- 确保结果的唯一性

## 📈 优势

1. **覆盖面更广**：同时搜索番号标题和演员名字
2. **结果准确**：自动去重，避免重复记录
3. **性能良好**：单次查询，减少数据库访问
4. **向后兼容**：保持原有API接口不变

---

**🎉 现在你可以用一个函数同时搜索番号和演员了！**
