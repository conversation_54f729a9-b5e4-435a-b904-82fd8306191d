import pymongo
import pandas as pd
from typing import List, Dict, Optional
import logging
from datetime import datetime
import sqlite3
import json
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SehuatangDatabase:
    """色花堂数据库访问类（支持MongoDB和SQLite）"""

    def __init__(self,
                 use_sqlite: bool = False,
                 sqlite_path: str = "./database_tools/converted_data/sehuatang.db",
                 connection_string: str = None,
                 database_name: str = "sehuatang"):
        """
        初始化数据库连接

        Args:
            use_sqlite: 是否使用SQLite数据库（默认False，使用MongoDB）
            sqlite_path: SQLite数据库文件路径
            connection_string: MongoDB连接字符串
            database_name: 数据库名，默认'sehuatang'
        """
        self.use_sqlite = use_sqlite
        self.sqlite_path = sqlite_path

        if use_sqlite:
            # SQLite模式
            self.connection = None
            self.client = None
            self.db = None
        else:
            # MongoDB模式
            self.connection_string = connection_string or "mongodb+srv://readonly:<EMAIL>/Cluster0?retryWrites=true&w=majority"
            self.database_name = database_name
            self.client = None
            self.db = None
            self.connection = None

    def connect(self):
        """建立数据库连接"""
        if self.use_sqlite:
            # SQLite连接
            try:
                if not os.path.exists(self.sqlite_path):
                    logger.error(f"SQLite数据库文件不存在: {self.sqlite_path}")
                    return False

                self.connection = sqlite3.connect(self.sqlite_path)
                self.connection.row_factory = sqlite3.Row  # 使结果可以按列名访问
                logger.info("SQLite连接成功")
                return True
            except Exception as e:
                logger.error(f"SQLite连接失败: {e}")
                return False
        else:
            # MongoDB连接
            try:
                self.client = pymongo.MongoClient(self.connection_string)
                self.db = self.client[self.database_name]

                # 测试连接
                self.client.admin.command('ping')
                logger.info("MongoDB连接成功")
                return True
            except Exception as e:
                logger.error(f"MongoDB连接失败: {e}")
                return False

    def disconnect(self):
        """关闭数据库连接"""
        if self.use_sqlite:
            if self.connection:
                self.connection.close()
                logger.info("SQLite连接已关闭")
        else:
            if self.client:
                self.client.close()
                logger.info("MongoDB连接已关闭")

    def get_collections(self) -> List[str]:
        """获取数据库中的所有集合/表名称"""
        if self.use_sqlite:
            if self.connection is None:
                if not self.connect():
                    return []
            try:
                cursor = self.connection.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [row[0] for row in cursor.fetchall()]
                return tables
            except Exception as e:
                logger.error(f"获取表列表失败: {e}")
                return []
        else:
            if self.db is None:
                if not self.connect():
                    return []
            try:
                return self.db.list_collection_names()
            except Exception as e:
                logger.error(f"获取集合列表失败: {e}")
                return []

    def find_documents(self, collection_name: str, query: Dict = None,
                      limit: int = 100, skip: int = 0, sort_field: str = None,
                      sort_direction: int = -1) -> List[Dict]:
        """
        查询文档

        Args:
            collection_name: 集合/表名称
            query: 查询条件
            limit: 限制返回条数
            skip: 跳过条数
            sort_field: 排序字段
            sort_direction: 排序方向 (-1降序, 1升序)

        Returns:
            文档列表
        """
        if self.use_sqlite:
            return self._find_documents_sqlite(collection_name, query, limit, skip, sort_field, sort_direction)
        else:
            return self._find_documents_mongodb(collection_name, query, limit, skip, sort_field, sort_direction)

    def _find_documents_mongodb(self, collection_name: str, query: Dict = None,
                               limit: int = 100, skip: int = 0, sort_field: str = None,
                               sort_direction: int = -1) -> List[Dict]:
        """MongoDB查询实现"""
        if self.db is None:
            if not self.connect():
                return []

        try:
            collection = self.db[collection_name]
            cursor = collection.find(query or {})

            if sort_field:
                cursor = cursor.sort(sort_field, sort_direction)

            cursor = cursor.skip(skip).limit(limit)

            # 转换ObjectId为字符串
            results = []
            for doc in cursor:
                if '_id' in doc:
                    doc['_id'] = str(doc['_id'])
                results.append(doc)

            return results
        except Exception as e:
            logger.error(f"MongoDB查询文档失败: {e}")
            return []

    def _find_documents_sqlite(self, table_name: str, query: Dict = None,
                              limit: int = 100, skip: int = 0, sort_field: str = None,
                              sort_direction: int = -1) -> List[Dict]:
        """SQLite查询实现"""
        if self.connection is None:
            if not self.connect():
                return []

        try:
            cursor = self.connection.cursor()

            # 构建SQL查询（使用反引号包围表名以处理特殊字符）
            sql = f"SELECT * FROM `{table_name}`"
            params = []

            # 处理查询条件
            if query:
                where_clauses = []
                for key, value in query.items():
                    if isinstance(value, dict):
                        # 处理MongoDB风格的查询操作符
                        if "$regex" in value:
                            where_clauses.append(f"{key} LIKE ?")
                            params.append(f"%{value['$regex']}%")
                        elif "$gte" in value:
                            where_clauses.append(f"{key} >= ?")
                            params.append(value["$gte"])
                        elif "$lte" in value:
                            where_clauses.append(f"{key} <= ?")
                            params.append(value["$lte"])
                        elif "$exists" in value:
                            if value["$exists"]:
                                where_clauses.append(f"{key} IS NOT NULL")
                            else:
                                where_clauses.append(f"{key} IS NULL")
                        elif "$ne" in value:
                            if value["$ne"] == "":
                                where_clauses.append(f"{key} != ''")
                            else:
                                where_clauses.append(f"{key} != ?")
                                params.append(value["$ne"])
                    elif key == "$or":
                        # 处理OR条件
                        or_clauses = []
                        for or_condition in value:
                            for or_key, or_value in or_condition.items():
                                if isinstance(or_value, dict) and "$regex" in or_value:
                                    or_clauses.append(f"{or_key} LIKE ?")
                                    params.append(f"%{or_value['$regex']}%")
                                else:
                                    or_clauses.append(f"{or_key} = ?")
                                    params.append(or_value)
                        if or_clauses:
                            where_clauses.append(f"({' OR '.join(or_clauses)})")
                    else:
                        where_clauses.append(f"{key} = ?")
                        params.append(value)

                if where_clauses:
                    sql += " WHERE " + " AND ".join(where_clauses)

            # 添加排序
            if sort_field:
                direction = "DESC" if sort_direction == -1 else "ASC"
                sql += f" ORDER BY {sort_field} {direction}"

            # 添加分页
            sql += f" LIMIT {limit} OFFSET {skip}"

            cursor.execute(sql, params)
            rows = cursor.fetchall()

            # 转换为字典列表
            results = []
            for row in rows:
                doc = dict(row)
                # 处理JSON字段
                for key, value in doc.items():
                    if isinstance(value, str) and (value.startswith('[') or value.startswith('{')):
                        try:
                            doc[key] = json.loads(value)
                        except:
                            pass
                results.append(doc)

            return results
        except Exception as e:
            logger.error(f"SQLite查询文档失败: {e}")
            return []

    def get_sht_data(self, limit: int = 100, skip: int = 0, collection_name: str = None) -> List[Dict]:
        """
        获取主要数据

        Args:
            limit: 限制返回条数
            skip: 跳过条数
            collection_name: 指定集合名称，如果不指定则从第一个集合获取

        Returns:
            数据列表
        """
        if collection_name:
            # 使用指定的集合
            return self.find_documents(
                collection_name,
                limit=limit,
                skip=skip,
                sort_field='post_time'
            )
        else:
            # 从第一个可用集合获取数据
            collections = self.get_collections()
            if collections:
                return self.find_documents(
                    collections[0],
                    limit=limit,
                    skip=skip,
                    sort_field='post_time'
                )

        return []

    def get_data_by_number(self, number: str, collection_name: str = None) -> List[Dict]:
        """
        根据番号查询数据

        Args:
            number: 番号
            collection_name: 集合名称，如果不指定则搜索所有集合

        Returns:
            匹配的数据列表
        """
        query = {"number": {"$regex": number, "$options": "i"}}

        if collection_name:
            return self.find_documents(collection_name, query)
        else:
            # 搜索所有集合
            all_results = []
            collections = self.get_collections()
            for col in collections:
                results = self.find_documents(col, query, limit=100)
                all_results.extend(results)
            return all_results

    def get_data_by_title(self, title: str, collection_name: str = None) -> List[Dict]:
        """
        根据标题搜索数据

        Args:
            title: 标题关键词
            collection_name: 集合名称，如果不指定则搜索所有集合

        Returns:
            匹配的数据列表
        """
        query = {"title": {"$regex": title, "$options": "i"}}

        if collection_name:
            return self.find_documents(collection_name, query)
        else:
            # 搜索所有集合
            all_results = []
            collections = self.get_collections()
            for col in collections:
                results = self.find_documents(col, query, limit=100)
                all_results.extend(results)
            return all_results

    def get_images_by_data_id(self, data_id: str) -> List[Dict]:
        """
        根据数据ID获取相关图片
        注意：根据实际数据库结构，图片信息可能直接存储在img字段中

        Args:
            data_id: 数据ID

        Returns:
            图片列表
        """
        # 根据实际观察，图片信息存储在文档的img字段中，而不是单独的集合
        # 这里保留接口以备将来可能的sht_images集合
        collections = self.get_collections()
        if 'sht_images' in collections:
            query = {"sht_data_id": data_id}
            return self.find_documents('sht_images', query)
        else:
            # 如果没有单独的图片集合，返回空列表
            # 图片信息在主文档的img字段中
            return []

    def get_data_with_images(self, data_id: str, collection_name: str = None) -> Dict:
        """
        获取数据及其相关图片

        Args:
            data_id: 数据ID
            collection_name: 指定集合名称

        Returns:
            包含数据和图片的字典
        """
        if collection_name:
            query = {"_id": data_id}
            data_result = self.find_documents(collection_name, query, limit=1)
        else:
            # 在所有集合中查找
            collections = self.get_collections()
            data_result = []
            for col in collections:
                query = {"_id": data_id}
                result = self.find_documents(col, query, limit=1)
                if result:
                    data_result = result
                    break

        if not data_result:
            return {}

        data = data_result[0]

        # 获取相关图片（如果有单独的图片集合）
        images = self.get_images_by_data_id(data_id)
        if images:
            data['images'] = images
        # 注意：图片信息通常已经在img字段中

        return data

    def get_statistics(self) -> Dict:
        """
        获取数据库统计信息

        Returns:
            统计信息字典
        """
        stats = {}

        try:
            collections = self.get_collections()
            stats['collections'] = collections

            for collection_name in collections:
                if self.db is None:
                    continue

                collection = self.db[collection_name]
                count = collection.count_documents({})
                stats[f'{collection_name}_count'] = count

                # 尝试获取最新文档的时间
                try:
                    latest_doc = collection.find().sort("post_time", -1).limit(1)
                    latest_doc = list(latest_doc)
                    if latest_doc and 'post_time' in latest_doc[0]:
                        stats[f'{collection_name}_latest_time'] = latest_doc[0]['post_time']
                except:
                    pass

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")

        return stats

    def to_dataframe(self, data: List[Dict]) -> pd.DataFrame:
        """
        将查询结果转换为pandas DataFrame

        Args:
            data: 查询结果

        Returns:
            DataFrame对象
        """
        return pd.DataFrame(data)

    def search_text(self, text: str, collection_name: str = None) -> List[Dict]:
        """
        全文搜索

        Args:
            text: 搜索文本
            collection_name: 集合名称，如果不指定则搜索所有集合

        Returns:
            匹配的文档列表
        """
        # 在多个字段中搜索
        query = {
            "$or": [
                {"title": {"$regex": text, "$options": "i"}},
                {"number": {"$regex": text, "$options": "i"}}
            ]
        }

        if collection_name:
            return self.find_documents(collection_name, query)
        else:
            # 搜索所有集合
            all_results = []
            collections = self.get_collections()
            for col in collections:
                results = self.find_documents(col, query, limit=50)
                all_results.extend(results)
            return all_results

    # FID到集合名称的映射（基于你的项目代码）
    @staticmethod
    def get_collection_name_by_fid(fid: int) -> str:
        """根据FID获取集合名称"""
        fid_mapping = {
            103: "hd_chinese_subtitles",      # 高清中文字幕
            104: "vegan_with_mosaic",         # 素人有码
            37: "asia_mosaic_originate",      # 亚洲有码原创
            36: "asia_codeless_originate",    # 亚洲无码原创
            39: "anime_originate",            # 动漫原创
            160: "vr_video",                  # VR视频
            151: "4k_video",                  # 4K视频
            2: "domestic_original",           # 国产原创
            38: "EU_US_no_mosaic",           # 欧美无码
            107: "three_levels_photo",        # 三级写真
            152: "korean_anchorman"           # 韩国主播
        }
        return fid_mapping.get(fid, "other")

    def get_data_by_fid(self, fid: int, limit: int = 100, skip: int = 0) -> List[Dict]:
        """根据FID获取对应集合的数据"""
        collection_name = self.get_collection_name_by_fid(fid)
        if collection_name == "other":
            logger.warning(f"未知的FID: {fid}")
            return []
        return self.get_sht_data(limit=limit, skip=skip, collection_name=collection_name)

    def get_data_by_date(self, date_str: str, collection_name: str = None, limit: int = 100) -> List[Dict]:
        """根据日期获取数据"""
        query = {"post_time": {"$regex": f"^{date_str}"}}

        if collection_name:
            return self.find_documents(collection_name, query, limit=limit)
        else:
            # 搜索所有集合
            all_results = []
            collections = self.get_collections()
            for col in collections:
                results = self.find_documents(col, query, limit=limit)
                all_results.extend(results)
            return all_results

    def get_data_by_tid(self, tid: int, collection_name: str = None) -> List[Dict]:
        """根据TID获取数据"""
        query = {"tid": tid}

        if collection_name:
            return self.find_documents(collection_name, query, limit=1)
        else:
            # 搜索所有集合
            collections = self.get_collections()
            for col in collections:
                results = self.find_documents(col, query, limit=1)
                if results:
                    return results
            return []

    # 便捷方法：针对具体集合的数据获取
    def get_domestic_original(self, limit: int = 100, skip: int = 0) -> List[Dict]:
        """获取国产原创数据 (FID: 2)"""
        return self.get_sht_data(limit=limit, skip=skip, collection_name='domestic_original')

    def get_asia_mosaic(self, limit: int = 100, skip: int = 0) -> List[Dict]:
        """获取亚洲有码原创数据 (FID: 37)"""
        return self.get_sht_data(limit=limit, skip=skip, collection_name='asia_mosaic_originate')

    def get_asia_codeless(self, limit: int = 100, skip: int = 0) -> List[Dict]:
        """获取亚洲无码原创数据 (FID: 36)"""
        return self.get_sht_data(limit=limit, skip=skip, collection_name='asia_codeless_originate')

    def get_hd_chinese_subtitles(self, limit: int = 100, skip: int = 0) -> List[Dict]:
        """获取高清中文字幕数据 (FID: 103)"""
        return self.get_sht_data(limit=limit, skip=skip, collection_name='hd_chinese_subtitles')

    def get_vr_video(self, limit: int = 100, skip: int = 0) -> List[Dict]:
        """获取VR视频数据 (FID: 160)"""
        return self.get_sht_data(limit=limit, skip=skip, collection_name='vr_video')

    def get_4k_video(self, limit: int = 100, skip: int = 0) -> List[Dict]:
        """获取4K视频数据 (FID: 151)"""
        return self.get_sht_data(limit=limit, skip=skip, collection_name='4k_video')

    def get_eu_us_no_mosaic(self, limit: int = 100, skip: int = 0) -> List[Dict]:
        """获取欧美无码数据 (FID: 38)"""
        return self.get_sht_data(limit=limit, skip=skip, collection_name='EU_US_no_mosaic')

    def get_three_levels_photo(self, limit: int = 100, skip: int = 0) -> List[Dict]:
        """获取三级写真数据 (FID: 107)"""
        return self.get_sht_data(limit=limit, skip=skip, collection_name='three_levels_photo')

    def search_movie_by_number(self, number: str, fid: int = None, collection_name: str = None, exact_match: bool = False) -> List[Dict]:
        """
        根据番号搜索影片信息的专用接口

        Args:
            number: 影片番号 (支持模糊匹配)
            fid: 可选的分区ID (如果提供，会转换为对应的集合名称)
            collection_name: 可选的集合名称 (如果提供fid，此参数会被忽略)
            exact_match: 是否精确匹配番号开头 (默认False为模糊匹配)

        Returns:
            符合条件的影片信息字典列表，每个字典包含以下字段：
            - _id: 文档ID
            - number: 番号 (实际包含番号+标题)
            - title: 演员名字
            - magnet: 磁力链接
            - post_time: 发布时间
            - date: 日期
            - tid: 页面标识
            - img: 图片URL列表
            - collection: 所属集合名称 (新增字段，便于识别来源)
            - extracted_number: 提取的纯番号 (新增字段)
        """
        if self.db is None:
            if not self.connect():
                return []

        # 构建查询条件
        if exact_match:
            # 精确匹配：番号必须在开头
            query = {"number": {"$regex": f"^{number}", "$options": "i"}}
        else:
            # 模糊匹配：包含即可
            query = {"number": {"$regex": number, "$options": "i"}}

        results = []

        # 如果指定了fid，转换为集合名称
        if fid is not None:
            collection_name = self.get_collection_name_by_fid(fid)
            if collection_name == "other":
                logger.warning(f"未知的FID: {fid}")
                return []

        if collection_name:
            # 在指定集合中搜索
            collection_results = self.find_documents(collection_name, query, limit=100)
            for item in collection_results:
                item['collection'] = collection_name  # 添加集合信息
                item['extracted_number'] = self._extract_number(item.get('number', ''))
                results.append(item)
        else:
            # 在所有集合中搜索
            collections = self.get_collections()
            for col in collections:
                collection_results = self.find_documents(col, query, limit=50)
                for item in collection_results:
                    item['collection'] = col  # 添加集合信息
                    item['extracted_number'] = self._extract_number(item.get('number', ''))
                    results.append(item)

        # 按发布时间降序排序
        try:
            results.sort(key=lambda x: x.get('post_time', ''), reverse=True)
        except:
            pass

        logger.info(f"番号 '{number}' 搜索完成，找到 {len(results)} 条结果")
        return results

    def _extract_number(self, full_number: str) -> str:
        """
        从完整的number字段中提取纯番号

        Args:
            full_number: 完整的number字段内容

        Returns:
            提取的纯番号
        """
        import re

        # 常见番号格式：字母-数字 或 字母数字
        patterns = [
            r'^([A-Z]+[-_]?\d+)',  # SSIS-123, MIDE-964 等
            r'^([A-Z]+\d+)',       # SSIS123 等
            r'^(\d+[A-Z]+[-_]?\d+)', # 123ABC-456 等
        ]

        for pattern in patterns:
            match = re.search(pattern, full_number, re.IGNORECASE)
            if match:
                return match.group(1).upper()

        # 如果没有匹配到标准格式，返回前20个字符
        return full_number[:20] if full_number else ""

    def search_by_pure_number(self, number: str, fid: int = None, collection_name: str = None) -> List[Dict]:
        """
        根据纯番号搜索 (精确匹配番号开头)

        Args:
            number: 纯番号 (如 SSIS-123)
            fid: 可选的分区ID
            collection_name: 可选的集合名称

        Returns:
            匹配的影片信息列表
        """
        return self.search_movie_by_number(number, fid, collection_name, exact_match=True)

    def search_by_content(self, keyword: str, fid: int = None, collection_name: str = None) -> List[Dict]:
        """
        根据内容关键词搜索 (同时在number字段和title字段中搜索)

        Args:
            keyword: 内容关键词 (如 "上司", "美女" 等)
            fid: 可选的分区ID
            collection_name: 可选的集合名称

        Returns:
            匹配的影片信息列表 (已去重)
        """
        if self.db is None:
            if not self.connect():
                return []

        # 构建查询条件 - 同时搜索number和title字段
        query = {
            "$or": [
                {"number": {"$regex": keyword, "$options": "i"}},
                {"title": {"$regex": keyword, "$options": "i"}}
            ]
        }

        results = []

        # 如果指定了fid，转换为集合名称
        if fid is not None:
            collection_name = self.get_collection_name_by_fid(fid)
            if collection_name == "other":
                logger.warning(f"未知的FID: {fid}")
                return []

        if collection_name:
            # 在指定集合中搜索
            collection_results = self.find_documents(collection_name, query, limit=100)
            for item in collection_results:
                item['collection'] = collection_name  # 添加集合信息
                item['extracted_number'] = self._extract_number(item.get('number', ''))
                results.append(item)
        else:
            # 在所有集合中搜索
            collections = self.get_collections()
            for col in collections:
                collection_results = self.find_documents(col, query, limit=50)
                for item in collection_results:
                    item['collection'] = col  # 添加集合信息
                    item['extracted_number'] = self._extract_number(item.get('number', ''))
                    results.append(item)

        # 去重 - 基于tid字段去重
        seen_tids = set()
        unique_results = []
        for item in results:
            tid = item.get('tid')
            if tid and tid not in seen_tids:
                seen_tids.add(tid)
                unique_results.append(item)
            elif not tid:  # 如果没有tid，基于_id去重
                doc_id = item.get('_id')
                if doc_id and doc_id not in seen_tids:
                    seen_tids.add(doc_id)
                    unique_results.append(item)

        # 按发布时间降序排序
        try:
            unique_results.sort(key=lambda x: x.get('post_time', ''), reverse=True)
        except:
            pass

        logger.info(f"内容关键词 '{keyword}' 搜索完成，找到 {len(unique_results)} 条去重后结果 (原始结果 {len(results)} 条)")
        return unique_results

    def get_movie_details(self, tid: int) -> Dict:
        """
        根据TID获取影片详细信息

        Args:
            tid: 影片的唯一标识ID

        Returns:
            影片详细信息字典，如果未找到返回空字典
        """
        result = self.get_data_by_tid(tid)
        if result:
            movie = result[0]
            # 添加一些额外的处理
            if 'img' in movie and isinstance(movie['img'], list):
                movie['image_count'] = len(movie['img'])
            else:
                movie['image_count'] = 0

            movie['has_magnet'] = bool(movie.get('magnet'))
            return movie
        return {}

    def search_movies_advanced(self,
                             number: str = None,
                             title: str = None,
                             fid: int = None,
                             date_from: str = None,
                             date_to: str = None,
                             has_magnet: bool = None,
                             has_images: bool = None,
                             limit: int = 100) -> List[Dict]:
        """
        高级搜索接口

        Args:
            number: 番号关键词
            title: 标题关键词
            fid: 分区ID
            date_from: 开始日期 (格式: YYYY-MM-DD)
            date_to: 结束日期 (格式: YYYY-MM-DD)
            has_magnet: 是否必须有磁力链接
            has_images: 是否必须有图片
            limit: 返回结果数量限制

        Returns:
            符合条件的影片信息列表
        """
        if self.db is None:
            if not self.connect():
                return []

        # 构建查询条件
        query = {}

        # 番号条件
        if number:
            query["number"] = {"$regex": number, "$options": "i"}

        # 标题条件
        if title:
            query["title"] = {"$regex": title, "$options": "i"}

        # 日期范围条件
        if date_from or date_to:
            date_query = {}
            if date_from:
                date_query["$gte"] = date_from
            if date_to:
                date_query["$lte"] = date_to + " 23:59:59"  # 包含整天
            query["post_time"] = date_query

        # 磁力链接条件
        if has_magnet is not None:
            if has_magnet:
                query["magnet"] = {"$exists": True, "$ne": ""}
            else:
                query["$or"] = [
                    {"magnet": {"$exists": False}},
                    {"magnet": ""}
                ]

        # 图片条件
        if has_images is not None:
            if has_images:
                query["img"] = {"$exists": True, "$ne": []}
            else:
                query["$or"] = [
                    {"img": {"$exists": False}},
                    {"img": []}
                ]

        results = []

        # 确定搜索范围
        if fid is not None:
            collection_name = self.get_collection_name_by_fid(fid)
            if collection_name == "other":
                logger.warning(f"未知的FID: {fid}")
                return []
            collections = [collection_name]
        else:
            collections = self.get_collections()

        # 执行搜索
        for col in collections:
            collection_results = self.find_documents(col, query, limit=limit//len(collections) + 10)
            for item in collection_results:
                item['collection'] = col
                # 添加便于使用的字段
                if 'img' in item and isinstance(item['img'], list):
                    item['image_count'] = len(item['img'])
                else:
                    item['image_count'] = 0
                item['has_magnet'] = bool(item.get('magnet'))
                results.append(item)

        # 排序并限制结果数量
        try:
            results.sort(key=lambda x: x.get('post_time', ''), reverse=True)
        except:
            pass

        return results[:limit]


# 为了向后兼容，保留原来的类名作为别名
SehuatangMongoDB = SehuatangDatabase

def main():
    """示例使用方法"""
    print("=== MongoDB模式示例 ===")
    # 使用MongoDB连接
    db_mongo = SehuatangDatabase(use_sqlite=False)

    try:
        if db_mongo.connect():
            print("MongoDB连接成功！")
            collections = db_mongo.get_collections()
            print(f"MongoDB集合: {collections[:3]}...")  # 只显示前3个

            # 获取一些数据
            data = db_mongo.get_sht_data(limit=3)
            print(f"MongoDB数据样例: {len(data)} 条")
        else:
            print("MongoDB连接失败")
    except Exception as e:
        print(f"MongoDB测试失败: {e}")
    finally:
        db_mongo.disconnect()

    print("\n=== SQLite模式示例 ===")
    # 使用SQLite连接
    db_sqlite = SehuatangDatabase(use_sqlite=True)

    try:
        if db_sqlite.connect():
            print("SQLite连接成功！")
            tables = db_sqlite.get_collections()
            print(f"SQLite表: {tables[:3]}...")  # 只显示前3个

            # 获取一些数据
            data = db_sqlite.get_sht_data(limit=3)
            print(f"SQLite数据样例: {len(data)} 条")

            # 搜索示例
            search_result = db_sqlite.get_data_by_number("SSIS")
            print(f"搜索番号 'SSIS' 结果: {len(search_result)} 条")
        else:
            print("SQLite连接失败（可能数据库文件不存在）")
    except Exception as e:
        print(f"SQLite测试失败: {e}")
    finally:
        db_sqlite.disconnect()

    print("\n=== 使用建议 ===")
    print("1. 在线使用：SehuatangDatabase(use_sqlite=False)  # 默认")
    print("2. 离线使用：SehuatangDatabase(use_sqlite=True)")
    print("3. 向后兼容：SehuatangMongoDB() 等同于 SehuatangDatabase(use_sqlite=False)")


if __name__ == "__main__":
    main()