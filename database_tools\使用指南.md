# 🔄 数据库工具快速使用指南

## 📁 文件说明
- `update_database.py` - 主要更新脚本（纯Python）
- `read_db_sqlite.py` - SQLite数据库访问接口

## 🚀 快速开始
```bash
# 进入工具目录
cd database_tools

# 运行更新脚本
python update_database.py
```

## ⚙️ 配置
如果mongodump路径不对，编辑 `update_database.py` 文件第30行：
```python
MONGODUMP_PATH = r"你的mongodump路径"
```

## 📊 输出
更新完成后会在 `../converted_data/` 文件夹生成 `sehuatang.db` 文件。

## 💡 使用SQLite数据库
```python
from read_db_sqlite import SehuatangSQLite

db = SehuatangSQLite()
if db.connect():
    # 获取数据
    data = db.get_sht_data(limit=10)
    print(data)
```

## 🔄 更新频率
建议每周运行一次 `python update_database.py` 来获取最新数据。
