#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
色花堂MongoDB数据库清理版示例
使用确定的集合名称和结构
"""

from read_db import SehuatangMongoDB

def main():
    """清理版示例 - 使用确定的数据库结构"""

    # 创建数据库连接
    db = SehuatangMongoDB(use_sqlite=True)

    try:
        if db.connect():
            print("✓ 数据库连接成功!")

            # 1. 显示数据库结构
            print("\n=== 数据库结构 ===")
            collections = db.get_collections()
            stats = db.get_statistics()

            print("集合及数据量:")
            collection_info = {
                'domestic_original': '国产原创',
                'asia_mosaic_originate': '亚洲有码原创',
                'asia_codeless_originate': '亚洲无码原创',
                'hd_chinese_subtitles': '高清中文字幕',
                'old_backup': '旧数据备份',
                'three_levels_photo': '三级写真',
                'vr_video': 'VR视频',
                'EU_US_no_mosaic': '欧美无码',
                'vegan_with_mosaic': '素人有码',
                '4k_video': '4K视频',
                'anime_originate': '动漫原创'
            }

            for col_name, col_desc in collection_info.items():
                count = stats.get(f'{col_name}_count', 0)
                print(f"  {col_desc}: {count:,} 条")

            # 2. 获取各分类样本数据
            print("\n=== 各分类样本数据 ===")

            # 国产原创
            print("国产原创 (最新2条):")
            domestic_data = db.get_domestic_original(limit=2)
            for i, item in enumerate(domestic_data, 1):
                print(f"  {i}. {item.get('number', 'N/A')[:60]}...")
                print(f"     时间: {item.get('post_time', 'N/A')}")

            # 亚洲无码
            print("\n亚洲无码 (最新2条):")
            asia_data = db.get_asia_codeless(limit=2)
            for i, item in enumerate(asia_data, 1):
                print(f"  {i}. {item.get('number', 'N/A')[:60]}...")
                print(f"     时间: {item.get('post_time', 'N/A')}")

            # 高清中文字幕
            print("\n高清中文字幕 (最新2条):")
            hd_data = db.get_hd_chinese_subtitles(limit=2)
            for i, item in enumerate(hd_data, 1):
                print(f"  {i}. {item.get('number', 'N/A')[:60]}...")
                print(f"     时间: {item.get('post_time', 'N/A')}")

            # 3. 搜索功能演示
            print("\n=== 搜索功能演示 ===")

            # 全库搜索
            all_results = db.get_data_by_title("美女")
            print(f"全库搜索'美女': {len(all_results)} 条结果")

            # 指定分类搜索
            domestic_results = db.get_data_by_title("美女", collection_name="domestic_original")
            print(f"国产原创中搜索'美女': {len(domestic_results)} 条结果")

            asia_results = db.get_data_by_title("美女", collection_name="asia_codeless_originate")
            print(f"亚洲无码中搜索'美女': {len(asia_results)} 条结果")

            # 4. 数据字段分析
            print("\n=== 数据字段分析 ===")
            if domestic_data:
                sample = domestic_data[0]
                print("数据字段:")
                for key, value in sample.items():
                    if isinstance(value, str) and len(value) > 50:
                        value = value[:50] + "..."
                    elif isinstance(value, list) and len(value) > 0:
                        value = f"[数组，{len(value)}个元素] {value[0] if value else ''}"
                    print(f"  {key}: {value}")

            # 5. 导出示例
            print("\n=== 数据导出示例 ===")

            # 获取国产原创数据并导出
            export_data = db.get_domestic_original(limit=100)
            if export_data:
                df = db.to_dataframe(export_data)
                df.to_csv('domestic_original_sample.csv', index=False, encoding='utf-8-sig')
                print(f"✓ 已导出国产原创样本数据到 domestic_original_sample.csv ({len(export_data)} 条)")

            # 获取4K视频数据并导出
            vr_data = db.get_4k_video(limit=50)
            if vr_data:
                df = db.to_dataframe(vr_data)
                df.to_csv('4k_video_sample.csv', index=False, encoding='utf-8-sig')
                print(f"✓ 已导出4K视频样本数据到 4k_video_sample.csv ({len(vr_data)} 条)")

        else:
            print("✗ 数据库连接失败")

    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.disconnect()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    print("色花堂MongoDB数据库 - 清理版示例")
    print("=" * 50)
    main()
