#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
色花堂MongoDB数据库访问示例
"""

from read_db import SehuatangMongoDB
import json

def main():
    """主函数 - 演示如何使用MongoDB数据库访问类"""

    # 使用提供的公开MongoDB数据库
    # 连接字符串已经内置在类中
    db = SehuatangMongoDB()

    try:
        # 测试连接
        print("正在连接MongoDB数据库...")
        if not db.connect():
            print("数据库连接失败，请检查网络连接")
            return

        print("MongoDB连接成功！")

        # 1. 获取数据库信息
        print("\n=== 数据库信息 ===")
        collections = db.get_collections()
        print(f"数据库中的集合: {collections}")

        # 2. 获取数据库统计信息
        print("\n=== 数据库统计信息 ===")
        stats = db.get_statistics()
        for key, value in stats.items():
            print(f"{key}: {value}")

        # 3. 获取最新的5条数据
        print("\n=== 最新5条数据 ===")
        latest_data = db.get_sht_data(limit=5)
        for i, item in enumerate(latest_data, 1):
            print(f"{i}. 文档ID: {item.get('_id', 'N/A')}")
            print(f"   标题: {item.get('title', 'N/A')[:50]}...")
            print(f"   番号: {item.get('number', 'N/A')}")
            print(f"   发布时间: {item.get('post_time', 'N/A')}")
            if item.get('magnet'):
                print(f"   磁力链接: {item['magnet'][:50]}...")
            print()

        # 4. 搜索示例 - 根据番号搜索
        print("\n=== 番号搜索示例 ===")
        search_number = "SSIS"  # 可以修改为其他番号
        search_results = db.get_data_by_number(search_number)
        print(f"搜索番号 '{search_number}' 找到 {len(search_results)} 条结果")

        for item in search_results[:3]:  # 只显示前3条
            print(f"- {item.get('number', 'N/A')}: {item.get('title', 'N/A')}")

        # 5. 标题搜索示例
        print("\n=== 标题搜索示例 ===")
        search_title = "美女"  # 可以修改为其他关键词
        title_results = db.get_data_by_title(search_title)
        print(f"搜索标题包含 '{search_title}' 找到 {len(title_results)} 条结果")

        for item in title_results[:3]:  # 只显示前3条
            print(f"- {item.get('title', 'N/A')[:50]}...")

        # 6. 全文搜索示例
        print("\n=== 全文搜索示例 ===")
        text_results = db.search_text("日本")
        print(f"全文搜索 '日本' 找到 {len(text_results)} 条结果")

        # 7. 获取数据及其图片
        if latest_data:
            print("\n=== 数据详情（包含图片）===")
            first_data_id = latest_data[0].get('_id') or latest_data[0].get('id')
            if first_data_id:
                data_with_images = db.get_data_with_images(first_data_id)

                if data_with_images:
                    print(f"数据ID: {data_with_images.get('_id', 'N/A')}")
                    print(f"标题: {data_with_images.get('title', 'N/A')}")
                    print(f"番号: {data_with_images.get('number', 'N/A')}")
                    print(f"相关图片数量: {len(data_with_images.get('images', []))}")

                    # 显示图片URL
                    for img in data_with_images.get('images', [])[:3]:  # 只显示前3张
                        print(f"  图片: {img.get('image_url', 'N/A')}")

        # 8. 转换为DataFrame进行数据分析
        print("\n=== 数据分析示例 ===")
        if latest_data:
            df = db.to_dataframe(latest_data)
            print(f"DataFrame形状: {df.shape}")
            print("列名:", list(df.columns))

            # 统计不同字段的数据
            for col in ['fid', 'tid', 'number']:
                if col in df.columns:
                    value_counts = df[col].value_counts()
                    if not value_counts.empty:
                        print(f"\n{col} 统计 (前5项):")
                        for value, count in value_counts.head().items():
                            print(f"  {value}: {count} 条")

        # 9. 导出数据示例
        print("\n=== 数据导出示例 ===")
        if latest_data:
            # 保存为JSON文件
            with open('sample_data.json', 'w', encoding='utf-8') as f:
                json.dump(latest_data, f, ensure_ascii=False, indent=2, default=str)
            print("样本数据已保存到 sample_data.json")

            # 保存为CSV文件
            df = db.to_dataframe(latest_data)
            df.to_csv('sample_data.csv', index=False, encoding='utf-8-sig')
            print("样本数据已保存到 sample_data.csv")

    except Exception as e:
        print(f"操作过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 关闭数据库连接
        db.disconnect()
        print("\n数据库连接已关闭")


def test_connection_only():
    """仅测试MongoDB数据库连接"""
    print("测试MongoDB数据库连接...")

    db = SehuatangMongoDB()

    if db.connect():
        print("✓ MongoDB连接成功")

        # 获取集合列表
        collections = db.get_collections()
        print(f"✓ 找到 {len(collections)} 个集合: {collections}")

        # 简单查询测试
        if collections:
            test_data = db.find_documents(collections[0], limit=1)
            if test_data:
                print(f"✓ 查询测试成功，从 {collections[0]} 集合获取到数据")
                print(f"  样本文档字段: {list(test_data[0].keys())}")
            else:
                print("✗ 查询测试失败，集合可能为空")

        db.disconnect()
    else:
        print("✗ MongoDB连接失败")


def explore_database():
    """探索数据库结构"""
    print("探索数据库结构...")

    db = SehuatangMongoDB()

    if not db.connect():
        print("连接失败")
        return

    try:
        collections = db.get_collections()
        print(f"发现 {len(collections)} 个集合:")

        for collection_name in collections:
            print(f"\n=== 集合: {collection_name} ===")

            # 获取文档数量
            if db.db is not None:
                collection = db.db[collection_name]
                count = collection.count_documents({})
                print(f"文档数量: {count}")

                # 获取样本文档
                sample_docs = list(collection.find().limit(2))
                if sample_docs:
                    print("样本文档结构:")
                    for i, doc in enumerate(sample_docs, 1):
                        print(f"  文档 {i} 字段: {list(doc.keys())}")
                        # 显示部分字段内容
                        for key, value in list(doc.items())[:5]:
                            if isinstance(value, str) and len(value) > 50:
                                value = value[:50] + "..."
                            print(f"    {key}: {value}")
                        print()

    except Exception as e:
        print(f"探索过程中发生错误: {e}")
    finally:
        db.disconnect()


if __name__ == "__main__":
    print("色花堂MongoDB数据库访问示例")
    print("=" * 50)

    # 选择运行模式
    print("选择运行模式:")
    print("1: 完整示例")
    print("2: 仅测试连接")
    print("3: 探索数据库结构")

    mode = input("请输入选择 (1/2/3): ").strip()

    if mode == "2":
        test_connection_only()
    elif mode == "3":
        explore_database()
    else:
        main()
