#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
色花堂SQLite数据库访问类
基于原有的MongoDB接口，适配SQLite本地数据库
"""

import sqlite3
import pandas as pd
from typing import List, Dict, Optional
import logging
import json
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SehuatangSQLite:
    """色花堂SQLite数据库访问类"""

    def __init__(self, db_path: str = "./converted_data/sehuatang.db"):
        """
        初始化SQLite连接

        Args:
            db_path: SQLite数据库文件路径
        """
        self.db_path = db_path
        self.connection = None

    def connect(self):
        """建立数据库连接"""
        try:
            if not os.path.exists(self.db_path):
                logger.error(f"数据库文件不存在: {self.db_path}")
                return False
            
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # 使结果可以按列名访问
            logger.info("SQLite连接成功")
            return True
        except Exception as e:
            logger.error(f"SQLite连接失败: {e}")
            return False

    def disconnect(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            logger.info("SQLite连接已关闭")

    def get_collections(self) -> List[str]:
        """获取数据库中的所有表名称（对应MongoDB的集合）"""
        if self.connection is None:
            if not self.connect():
                return []
        try:
            cursor = self.connection.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in cursor.fetchall()]
            return tables
        except Exception as e:
            logger.error(f"获取表列表失败: {e}")
            return []

    def find_documents(self, table_name: str, query: Dict = None,
                      limit: int = 100, skip: int = 0, sort_field: str = None,
                      sort_direction: int = -1) -> List[Dict]:
        """
        查询文档

        Args:
            table_name: 表名称
            query: 查询条件（简化版，支持基本的字段匹配和LIKE）
            limit: 限制返回条数
            skip: 跳过条数
            sort_field: 排序字段
            sort_direction: 排序方向 (-1降序, 1升序)

        Returns:
            文档列表
        """
        if self.connection is None:
            if not self.connect():
                return []

        try:
            cursor = self.connection.cursor()
            
            # 构建SQL查询
            sql = f"SELECT * FROM {table_name}"
            params = []
            
            # 处理查询条件
            if query:
                where_clauses = []
                for key, value in query.items():
                    if isinstance(value, dict):
                        # 处理MongoDB风格的查询操作符
                        if "$regex" in value:
                            where_clauses.append(f"{key} LIKE ?")
                            params.append(f"%{value['$regex']}%")
                        elif "$gte" in value:
                            where_clauses.append(f"{key} >= ?")
                            params.append(value["$gte"])
                        elif "$lte" in value:
                            where_clauses.append(f"{key} <= ?")
                            params.append(value["$lte"])
                        elif "$exists" in value:
                            if value["$exists"]:
                                where_clauses.append(f"{key} IS NOT NULL")
                            else:
                                where_clauses.append(f"{key} IS NULL")
                        elif "$ne" in value:
                            if value["$ne"] == "":
                                where_clauses.append(f"{key} != ''")
                            else:
                                where_clauses.append(f"{key} != ?")
                                params.append(value["$ne"])
                    elif key == "$or":
                        # 处理OR条件
                        or_clauses = []
                        for or_condition in value:
                            for or_key, or_value in or_condition.items():
                                if isinstance(or_value, dict) and "$regex" in or_value:
                                    or_clauses.append(f"{or_key} LIKE ?")
                                    params.append(f"%{or_value['$regex']}%")
                                else:
                                    or_clauses.append(f"{or_key} = ?")
                                    params.append(or_value)
                        if or_clauses:
                            where_clauses.append(f"({' OR '.join(or_clauses)})")
                    else:
                        where_clauses.append(f"{key} = ?")
                        params.append(value)
                
                if where_clauses:
                    sql += " WHERE " + " AND ".join(where_clauses)
            
            # 添加排序
            if sort_field:
                direction = "DESC" if sort_direction == -1 else "ASC"
                sql += f" ORDER BY {sort_field} {direction}"
            
            # 添加分页
            sql += f" LIMIT {limit} OFFSET {skip}"
            
            cursor.execute(sql, params)
            rows = cursor.fetchall()
            
            # 转换为字典列表
            results = []
            for row in rows:
                doc = dict(row)
                # 处理JSON字段
                for key, value in doc.items():
                    if isinstance(value, str) and (value.startswith('[') or value.startswith('{')):
                        try:
                            doc[key] = json.loads(value)
                        except:
                            pass
                results.append(doc)
            
            return results
        except Exception as e:
            logger.error(f"查询文档失败: {e}")
            return []

    def get_sht_data(self, limit: int = 100, skip: int = 0, collection_name: str = None) -> List[Dict]:
        """
        获取主要数据

        Args:
            limit: 限制返回条数
            skip: 跳过条数
            collection_name: 指定表名称，如果不指定则从第一个表获取

        Returns:
            数据列表
        """
        if collection_name:
            return self.find_documents(
                collection_name,
                limit=limit,
                skip=skip,
                sort_field='post_time'
            )
        else:
            # 从第一个可用表获取数据
            tables = self.get_collections()
            if tables:
                return self.find_documents(
                    tables[0],
                    limit=limit,
                    skip=skip,
                    sort_field='post_time'
                )
        return []

    def get_data_by_number(self, number: str, collection_name: str = None) -> List[Dict]:
        """
        根据番号查询数据

        Args:
            number: 番号
            collection_name: 表名称，如果不指定则搜索所有表

        Returns:
            匹配的数据列表
        """
        query = {"number": {"$regex": number}}

        if collection_name:
            return self.find_documents(collection_name, query)
        else:
            # 搜索所有表
            all_results = []
            tables = self.get_collections()
            for table in tables:
                results = self.find_documents(table, query, limit=100)
                all_results.extend(results)
            return all_results

    def get_data_by_title(self, title: str, collection_name: str = None) -> List[Dict]:
        """
        根据标题搜索数据

        Args:
            title: 标题关键词
            collection_name: 表名称，如果不指定则搜索所有表

        Returns:
            匹配的数据列表
        """
        query = {"title": {"$regex": title}}

        if collection_name:
            return self.find_documents(collection_name, query)
        else:
            # 搜索所有表
            all_results = []
            tables = self.get_collections()
            for table in tables:
                results = self.find_documents(table, query, limit=100)
                all_results.extend(results)
            return all_results

    def get_statistics(self) -> Dict:
        """
        获取数据库统计信息

        Returns:
            统计信息字典
        """
        stats = {}
        
        try:
            tables = self.get_collections()
            stats['collections'] = tables
            
            if self.connection is None:
                if not self.connect():
                    return stats
            
            cursor = self.connection.cursor()
            
            for table_name in tables:
                # 获取记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                stats[f'{table_name}_count'] = count
                
                # 尝试获取最新文档的时间
                try:
                    cursor.execute(f"SELECT post_time FROM {table_name} ORDER BY post_time DESC LIMIT 1")
                    result = cursor.fetchone()
                    if result and result[0]:
                        stats[f'{table_name}_latest_time'] = result[0]
                except:
                    pass
        
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
        
        return stats

    def to_dataframe(self, data: List[Dict]) -> pd.DataFrame:
        """
        将查询结果转换为pandas DataFrame

        Args:
            data: 查询结果

        Returns:
            DataFrame对象
        """
        return pd.DataFrame(data)

    def search_text(self, text: str, collection_name: str = None) -> List[Dict]:
        """
        全文搜索

        Args:
            text: 搜索文本
            collection_name: 表名称，如果不指定则搜索所有表

        Returns:
            匹配的文档列表
        """
        # 在多个字段中搜索
        query = {
            "$or": [
                {"title": {"$regex": text}},
                {"number": {"$regex": text}}
            ]
        }

        if collection_name:
            return self.find_documents(collection_name, query)
        else:
            # 搜索所有表
            all_results = []
            tables = self.get_collections()
            for table in tables:
                results = self.find_documents(table, query, limit=50)
                all_results.extend(results)
            return all_results

    # FID到表名称的映射（与MongoDB版本保持一致）
    @staticmethod
    def get_collection_name_by_fid(fid: int) -> str:
        """根据FID获取表名称"""
        fid_mapping = {
            103: "hd_chinese_subtitles",      # 高清中文字幕
            104: "vegan_with_mosaic",         # 素人有码
            37: "asia_mosaic_originate",      # 亚洲有码原创
            36: "asia_codeless_originate",    # 亚洲无码原创
            39: "anime_originate",            # 动漫原创
            160: "vr_video",                  # VR视频
            151: "4k_video",                  # 4K视频
            2: "domestic_original",           # 国产原创
            38: "EU_US_no_mosaic",           # 欧美无码
            107: "three_levels_photo",        # 三级写真
            152: "korean_anchorman"           # 韩国主播
        }
        return fid_mapping.get(fid, "other")

    def get_data_by_fid(self, fid: int, limit: int = 100, skip: int = 0) -> List[Dict]:
        """根据FID获取对应表的数据"""
        collection_name = self.get_collection_name_by_fid(fid)
        if collection_name == "other":
            logger.warning(f"未知的FID: {fid}")
            return []
        return self.get_sht_data(limit=limit, skip=skip, collection_name=collection_name)


def main():
    """示例使用方法"""
    # 使用SQLite数据库
    db = SehuatangSQLite()
    
    try:
        # 连接数据库
        if db.connect():
            print("SQLite连接成功！")
            
            # 获取所有表
            tables = db.get_collections()
            print(f"数据库中的表: {tables}")
            
            # 获取统计信息
            stats = db.get_statistics()
            print(f"数据库统计信息: {stats}")
            
            # 获取最新的10条数据
            latest_data = db.get_sht_data(limit=10)
            print(f"获取到 {len(latest_data)} 条最新数据")
            
            # 显示数据样例
            if latest_data:
                print("\n=== 数据样例 ===")
                for i, item in enumerate(latest_data[:3], 1):
                    print(f"{i}. 文档ID: {item.get('_id', 'N/A')}")
                    print(f"   标题: {item.get('title', 'N/A')}")
                    print(f"   番号: {item.get('number', 'N/A')}")
                    print(f"   发布时间: {item.get('post_time', 'N/A')}")
                    if item.get('magnet'):
                        print(f"   磁力链接: {item['magnet'][:50]}...")
                    print()
    
    except Exception as e:
        print(f"操作失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 关闭连接
        db.disconnect()


if __name__ == "__main__":
    main()
