# 数据库配置示例
# 请复制此文件为 config.py 并填入实际的数据库连接信息

DATABASE_CONFIG = {
    'host': 'your_database_host',  # 数据库主机地址
    'port': 3306,                  # 数据库端口
    'user': 'sehuatang',          # 用户名
    'password': 'sehuatang',      # 密码
    'database': 'sehuatang'       # 数据库名
}

# 如果是公开数据库，可能的配置示例：
# DATABASE_CONFIG = {
#     'host': 'public.database.host',
#     'port': 3306,
#     'user': 'readonly',
#     'password': 'public_password',
#     'database': 'sehuatang'
# }
