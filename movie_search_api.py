#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
影片搜索API接口示例
提供根据番号搜索影片的专用接口
"""

from read_db import SehuatangMongoDB
from typing import List, Dict, Optional
import json

class MovieSearchAPI:
    """影片搜索API类"""

    def __init__(self):
        self.db = SehuatangMongoDB()
        self.connected = False

    def connect(self) -> bool:
        """连接数据库"""
        if not self.connected:
            self.connected = self.db.connect()
        return self.connected

    def disconnect(self):
        """断开数据库连接"""
        if self.connected:
            self.db.disconnect()
            self.connected = False

    def search_by_number(self, number: str, fid: Optional[int] = None, exact_match: bool = False) -> List[Dict]:
        """
        根据番号搜索影片 - 主要接口

        Args:
            number: 影片番号
            fid: 可选的分区ID
                - 2: 国产原创
                - 36: 亚洲无码原创
                - 37: 亚洲有码原创
                - 103: 高清中文字幕
                - 151: 4K视频
                - 160: VR视频
                - 38: 欧美无码
                - 107: 三级写真
                - 104: 素人有码
                - 39: 动漫原创
            exact_match: 是否精确匹配番号开头 (默认False为模糊匹配)

        Returns:
            符合条件的影片信息列表
        """
        if not self.connect():
            return []

        return self.db.search_movie_by_number(number, fid, exact_match=exact_match)

    def search_by_pure_number(self, number: str, fid: Optional[int] = None) -> List[Dict]:
        """
        根据纯番号搜索 (精确匹配番号开头)

        Args:
            number: 纯番号 (如 SSIS-123, MIDE-964)
            fid: 可选的分区ID

        Returns:
            匹配的影片信息列表
        """
        if not self.connect():
            return []

        return self.db.search_by_pure_number(number, fid)

    def search_by_content(self, keyword: str, fid: Optional[int] = None) -> List[Dict]:
        """
        根据内容关键词搜索 (搜索标题和番号内容)

        Args:
            keyword: 内容关键词 (如 "上司", "美女", "学生" 等)
            fid: 可选的分区ID

        Returns:
            匹配的影片信息列表
        """
        if not self.connect():
            return []

        return self.db.search_by_content(keyword, fid)

    def search_by_actor(self, actor_name: str, fid: Optional[int] = None) -> List[Dict]:
        """
        根据演员名字搜索

        Args:
            actor_name: 演员名字
            fid: 可选的分区ID

        Returns:
            匹配的影片信息列表
        """
        if not self.connect():
            return []

        return self.db.get_data_by_title(actor_name,
                                        self.db.get_collection_name_by_fid(fid) if fid else None)

    def get_movie_by_tid(self, tid: int) -> Dict:
        """
        根据TID获取影片详细信息

        Args:
            tid: 影片的唯一标识ID

        Returns:
            影片详细信息字典
        """
        if not self.connect():
            return {}

        return self.db.get_movie_details(tid)

    def advanced_search(self, **kwargs) -> List[Dict]:
        """
        高级搜索接口

        支持的参数:
            number: 番号关键词
            title: 标题关键词
            fid: 分区ID
            date_from: 开始日期 (YYYY-MM-DD)
            date_to: 结束日期 (YYYY-MM-DD)
            has_magnet: 是否必须有磁力链接
            has_images: 是否必须有图片
            limit: 返回结果数量限制
        """
        if not self.connect():
            return []

        return self.db.search_movies_advanced(**kwargs)

    def get_fid_info(self) -> Dict[int, str]:
        """获取FID对应的分区信息"""
        return {
            2: "国产原创",
            36: "亚洲无码原创",
            37: "亚洲有码原创",
            103: "高清中文字幕",
            151: "4K视频",
            160: "VR视频",
            38: "欧美无码",
            107: "三级写真",
            104: "素人有码",
            39: "动漫原创"
        }

def demo_search():
    """演示搜索功能"""
    api = MovieSearchAPI()

    try:
        print("=== 影片搜索API演示 ===\n")

        # 1. 基本番号搜索
        print("1. 基本番号搜索")
        print("-" * 30)

        # 搜索包含"SSIS"的番号
        results = api.search_by_number("SSIS")
        print(f"搜索 'SSIS': 找到 {len(results)} 条结果")

        if results:
            for i, movie in enumerate(results[:3], 1):
                print(f"  {i}. 番号: {movie.get('number', 'N/A')}")
                print(f"     标题: {movie.get('title', 'N/A')[:50]}...")
                print(f"     分区: {movie.get('collection', 'N/A')}")
                print(f"     时间: {movie.get('post_time', 'N/A')}")
                print(f"     有磁力: {'是' if movie.get('has_magnet') else '否'}")
                print()

        # 2. 指定分区搜索
        print("2. 指定分区搜索")
        print("-" * 30)

        # 在国产原创分区搜索
        domestic_results = api.search_by_number("美女", fid=2)
        print(f"国产原创中搜索 '美女': 找到 {len(domestic_results)} 条结果")

        if domestic_results:
            movie = domestic_results[0]
            print(f"  示例: {movie.get('number', 'N/A')}")
            print(f"  时间: {movie.get('post_time', 'N/A')}")
            print(f"  图片数: {movie.get('image_count', 0)}")

        # 3. 高级搜索
        print("\n3. 高级搜索")
        print("-" * 30)

        # 搜索最近的4K视频
        advanced_results = api.advanced_search(
            fid=151,  # 4K视频分区
            has_magnet=True,  # 必须有磁力链接
            has_images=True,  # 必须有图片
            limit=5
        )
        print(f"4K视频分区中有磁力和图片的影片: {len(advanced_results)} 条")

        for i, movie in enumerate(advanced_results, 1):
            print(f"  {i}. {movie.get('number', 'N/A')[:40]}...")
            print(f"     时间: {movie.get('post_time', 'N/A')}")

        # 4. 根据TID获取详细信息
        if results:
            print("\n4. 获取详细信息")
            print("-" * 30)

            sample_tid = results[0].get('tid')
            if sample_tid:
                details = api.get_movie_by_tid(sample_tid)
                if details:
                    print(f"TID {sample_tid} 详细信息:")
                    print(f"  番号: {details.get('number', 'N/A')}")
                    print(f"  标题: {details.get('title', 'N/A')}")
                    print(f"  发布时间: {details.get('post_time', 'N/A')}")
                    print(f"  磁力链接: {'有' if details.get('has_magnet') else '无'}")
                    print(f"  图片数量: {details.get('image_count', 0)}")

                    # 显示图片URL (如果有)
                    if details.get('img'):
                        print("  图片URL:")
                        for i, img_url in enumerate(details['img'][:3], 1):
                            print(f"    {i}. {img_url}")

        # 5. 分区信息
        print("\n5. 可用分区信息")
        print("-" * 30)
        fid_info = api.get_fid_info()
        for fid, name in fid_info.items():
            print(f"  FID {fid}: {name}")

    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        api.disconnect()

def search_interface():
    """交互式搜索界面"""
    api = MovieSearchAPI()

    try:
        print("=== 影片搜索交互界面 ===")
        print("输入 'quit' 退出程序")
        print("输入 'help' 查看帮助")

        while True:
            print("\n" + "-" * 40)
            user_input = input("请输入番号关键词: ").strip()

            if user_input.lower() == 'quit':
                break
            elif user_input.lower() == 'help':
                print("\n帮助信息:")
                print("- 直接输入番号进行搜索")
                print("- 输入格式: 番号 [分区ID]")
                print("- 例如: SSIS 或 SSIS 103")
                print("- 可用分区ID:")
                fid_info = api.get_fid_info()
                for fid, name in fid_info.items():
                    print(f"  {fid}: {name}")
                continue
            elif not user_input:
                continue

            # 解析输入
            parts = user_input.split()
            number = parts[0]
            fid = None

            if len(parts) > 1:
                try:
                    fid = int(parts[1])
                except ValueError:
                    print("分区ID必须是数字")
                    continue

            # 执行搜索
            results = api.search_by_number(number, fid)

            if not results:
                print(f"未找到包含 '{number}' 的影片")
                continue

            print(f"\n找到 {len(results)} 条结果:")

            for i, movie in enumerate(results[:10], 1):  # 只显示前10条
                print(f"\n{i}. 番号: {movie.get('number', 'N/A')}")
                print(f"   标题: {movie.get('title', 'N/A')[:60]}...")
                print(f"   分区: {movie.get('collection', 'N/A')}")
                print(f"   时间: {movie.get('post_time', 'N/A')}")
                print(f"   TID: {movie.get('tid', 'N/A')}")
                print(f"   磁力: {'有' if movie.get('has_magnet') else '无'}")
                print(f"   图片: {movie.get('image_count', 0)} 张")

            if len(results) > 10:
                print(f"\n... 还有 {len(results) - 10} 条结果未显示")

    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"程序发生错误: {e}")
    finally:
        api.disconnect()
        print("数据库连接已关闭")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        search_interface()
    else:
        demo_search()
