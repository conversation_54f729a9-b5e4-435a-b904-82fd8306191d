#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
色花堂MongoDB数据库简单使用示例
"""

from read_db import SehuatangMongoDB

def main():
    """简单示例"""
    # 创建数据库连接
    db = SehuatangMongoDB()

    try:
        # 连接数据库
        if db.connect():
            print("✓ 数据库连接成功!")

            # 获取集合信息
            collections = db.get_collections()
            print(f"发现 {len(collections)} 个集合: {collections}")

            # 获取不同分类的数据
            print("\n=== 各分类最新数据 ===")

            # 国产原创
            domestic_data = db.get_domestic_original(limit=2)
            print(f"国产原创 (最新2条):")
            for i, item in enumerate(domestic_data, 1):
                print(f"  {i}. {item.get('number', 'N/A')[:50]}...")
                print(f"     时间: {item.get('post_time', 'N/A')}")

            # 亚洲无码
            asia_data = db.get_asia_codeless(limit=2)
            print(f"\n亚洲无码 (最新2条):")
            for i, item in enumerate(asia_data, 1):
                print(f"  {i}. {item.get('number', 'N/A')[:50]}...")
                print(f"     时间: {item.get('post_time', 'N/A')}")

            # 4K视频
            vr_data = db.get_4k_video(limit=2)
            print(f"\n4K视频 (最新2条):")
            for i, item in enumerate(vr_data, 1):
                print(f"  {i}. {item.get('number', 'N/A')[:50]}...")
                print(f"     时间: {item.get('post_time', 'N/A')}")

            # 搜索示例
            print("\n=== 搜索示例 ===")
            search_results = db.get_data_by_title("美女")
            print(f"全库搜索'美女'找到 {len(search_results)} 条结果")

            # 在特定分类中搜索
            domestic_search = db.get_data_by_title("美女", collection_name="domestic_original")
            print(f"国产原创中搜索'美女'找到 {len(domestic_search)} 条结果")

            # 数据统计
            print("\n=== 数据统计 ===")
            stats = db.get_statistics()
            total_count = sum(v for k, v in stats.items() if k.endswith('_count'))
            print(f"总数据量: {total_count:,} 条")

            print("\n各分类数据量:")
            for key, value in stats.items():
                if key.endswith('_count'):
                    category = key.replace('_count', '')
                    print(f"  {category}: {value:,} 条")

        else:
            print("✗ 数据库连接失败")

    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        db.disconnect()
        print("\n数据库连接已关闭")

if __name__ == "__main__":
    main()
