#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索功能演示 - 展示不同的搜索方式
解释数据结构和查询逻辑
"""

from movie_search_api import MovieSearchAPI

def explain_data_structure():
    """解释数据结构"""
    print("=== 数据库字段结构说明 ===")
    print()
    print("在这个数据库中，字段的实际含义与常规理解不同：")
    print()
    print("📋 字段说明：")
    print("- number: 存储 '番号 + 完整标题'，如 'SSIS-123【中文字幕】美女老师的诱惑'")
    print("- title:  存储 '演员名字'，如 '高桥圣子'")
    print("- tid:    唯一标识ID")
    print("- img:    图片URL数组")
    print("- magnet: 磁力链接")
    print()
    print("🔍 查询逻辑：")
    print("- 搜索番号时，实际是在 'number' 字段中搜索")
    print("- 由于 'number' 字段包含完整标题，所以会匹配到标题内容")
    print("- 这就是为什么搜索 'SSIS' 和搜索 '上司' 都能找到结果的原因")
    print()

def demo_different_searches():
    """演示不同的搜索方式"""
    api = MovieSearchAPI()
    
    try:
        print("=== 不同搜索方式演示 ===")
        print()
        
        # 1. 模糊搜索 (默认方式)
        print("1. 模糊搜索 - search_by_number()")
        print("-" * 40)
        results1 = api.search_by_number("SSIS", fid=103, exact_match=False)
        print(f"模糊搜索 'SSIS': 找到 {len(results1)} 条结果")
        if results1:
            print(f"示例: {results1[0]['extracted_number']} - {results1[0]['number'][:60]}...")
        print()
        
        # 2. 精确匹配番号开头
        print("2. 精确匹配 - search_by_pure_number()")
        print("-" * 40)
        results2 = api.search_by_pure_number("SSIS", fid=103)
        print(f"精确匹配 'SSIS' 开头: 找到 {len(results2)} 条结果")
        if results2:
            print(f"示例: {results2[0]['extracted_number']} - {results2[0]['number'][:60]}...")
        print()
        
        # 3. 内容关键词搜索
        print("3. 内容搜索 - search_by_content()")
        print("-" * 40)
        results3 = api.search_by_content("上司", fid=103)
        print(f"内容搜索 '上司': 找到 {len(results3)} 条结果")
        if results3:
            print(f"示例: {results3[0]['extracted_number']} - {results3[0]['number'][:60]}...")
        print()
        
        # 4. 演员名字搜索
        print("4. 演员搜索 - search_by_actor()")
        print("-" * 40)
        results4 = api.search_by_actor("高桥", fid=103)
        print(f"演员搜索 '高桥': 找到 {len(results4)} 条结果")
        if results4:
            print(f"示例: 演员 '{results4[0]['title']}' - {results4[0]['number'][:60]}...")
        print()
        
        # 5. 对比不同搜索结果
        print("5. 搜索结果对比")
        print("-" * 40)
        
        # 搜索具体番号
        specific_results = api.search_by_pure_number("SSIS-415")
        print(f"搜索具体番号 'SSIS-415': {len(specific_results)} 条结果")
        
        # 搜索关键词
        keyword_results = api.search_by_content("上司")
        print(f"搜索关键词 '上司': {len(keyword_results)} 条结果")
        
        # 显示详细对比
        if specific_results:
            item = specific_results[0]
            print(f"\n具体番号搜索结果:")
            print(f"  提取番号: {item['extracted_number']}")
            print(f"  完整信息: {item['number']}")
            print(f"  演员: {item['title']}")
            print(f"  TID: {item['tid']}")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        api.disconnect()

def demo_regex_explanation():
    """演示正则表达式查询"""
    print("=== MongoDB正则表达式查询解释 ===")
    print()
    print("🔧 查询语法:")
    print('query = {"number": {"$regex": "SSIS", "$options": "i"}}')
    print()
    print("📝 语法说明:")
    print("- $regex: MongoDB正则表达式操作符")
    print("- 'SSIS': 搜索模式 (支持正则表达式)")
    print("- $options: 'i' = 忽略大小写")
    print()
    print("🎯 匹配效果:")
    print("- 会匹配包含 'SSIS' 的所有记录 (不区分大小写)")
    print("- 例如: 'SSIS-123', 'ssis-456', 'ABC-SSIS-789' 都会匹配")
    print()
    print("🔍 精确匹配:")
    print('query = {"number": {"$regex": "^SSIS", "$options": "i"}}')
    print("- ^ 表示必须以 'SSIS' 开头")
    print("- 只会匹配: 'SSIS-123', 'SSIS456' 等")
    print("- 不会匹配: 'ABC-SSIS-789'")
    print()

def interactive_search_demo():
    """交互式搜索演示"""
    api = MovieSearchAPI()
    
    try:
        print("=== 交互式搜索演示 ===")
        print("输入 'quit' 退出")
        print()
        
        while True:
            print("选择搜索方式:")
            print("1. 精确番号搜索")
            print("2. 内容关键词搜索") 
            print("3. 演员名字搜索")
            print("4. 模糊搜索")
            
            choice = input("\n请选择 (1-4) 或输入 'quit': ").strip()
            
            if choice.lower() == 'quit':
                break
            
            if choice not in ['1', '2', '3', '4']:
                print("无效选择，请重试")
                continue
            
            keyword = input("请输入搜索关键词: ").strip()
            if not keyword:
                continue
            
            fid_input = input("请输入分区ID (可选，直接回车跳过): ").strip()
            fid = None
            if fid_input:
                try:
                    fid = int(fid_input)
                except ValueError:
                    print("分区ID必须是数字")
                    continue
            
            # 执行搜索
            if choice == '1':
                results = api.search_by_pure_number(keyword, fid)
                search_type = "精确番号搜索"
            elif choice == '2':
                results = api.search_by_content(keyword, fid)
                search_type = "内容关键词搜索"
            elif choice == '3':
                results = api.search_by_actor(keyword, fid)
                search_type = "演员名字搜索"
            else:
                results = api.search_by_number(keyword, fid, exact_match=False)
                search_type = "模糊搜索"
            
            print(f"\n{search_type} '{keyword}': 找到 {len(results)} 条结果")
            
            # 显示结果
            for i, movie in enumerate(results[:5], 1):
                print(f"\n{i}. 提取番号: {movie.get('extracted_number', 'N/A')}")
                print(f"   完整信息: {movie.get('number', 'N/A')[:70]}...")
                print(f"   演员: {movie.get('title', 'N/A')}")
                print(f"   分区: {movie.get('collection', 'N/A')}")
                print(f"   TID: {movie.get('tid', 'N/A')}")
            
            if len(results) > 5:
                print(f"\n... 还有 {len(results) - 5} 条结果未显示")
            
            print("\n" + "="*60)
    
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序发生错误: {e}")
    finally:
        api.disconnect()

def main():
    """主函数"""
    print("🎬 影片搜索功能详解")
    print("="*60)
    
    # 1. 解释数据结构
    explain_data_structure()
    
    # 2. 演示正则表达式
    demo_regex_explanation()
    
    # 3. 演示不同搜索方式
    demo_different_searches()
    
    # 4. 交互式演示
    choice = input("\n是否进入交互式搜索演示? (y/n): ").strip().lower()
    if choice == 'y':
        interactive_search_demo()

if __name__ == "__main__":
    main()
